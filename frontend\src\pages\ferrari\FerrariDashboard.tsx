/**
 * Ferrari Dashboard - Main Ferrari AI platform overview
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Brain,
  Zap,
  Target,
  Network,
  Shield,
  TrendingUp,
  Activity,
  CheckCircle
} from 'lucide-react';

export function FerrariDashboard() {
  const ferrariServices = [
    { name: 'Creative Exploits', status: 'online', confidence: 92, tasks: 247 },
    { name: 'Multi-Stage Orchestrator', status: 'online', confidence: 88, tasks: 43 },
    { name: 'Behavioral Analysis', status: 'online', confidence: 85, tasks: 89 },
    { name: 'AI Proxy', status: 'online', confidence: 90, tasks: 156 },
    { name: 'Evasion Generator', status: 'online', confidence: 87, tasks: 78 },
    { name: 'Adaptive Modifier', status: 'online', confidence: 89, tasks: 134 }
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Ferrari AI Platform</h1>
          <p className="text-muted-foreground">Advanced AI-powered security testing capabilities</p>
        </div>
        <div className="flex items-center gap-3">
          <Badge variant="outline" className="text-green-500">
            <Zap className="mr-1 h-3 w-3" />
            All Systems Online
          </Badge>
          <Button>
            <Brain className="mr-2 h-4 w-4" />
            New AI Task
          </Button>
        </div>
      </div>

      {/* AI Performance Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">AI Services</CardTitle>
            <Brain className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">6</div>
            <p className="text-xs text-muted-foreground">All online and ready</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">AI Confidence</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">88.5%</div>
            <p className="text-xs text-muted-foreground">Average across all services</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tasks Completed</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">747</div>
            <p className="text-xs text-muted-foreground">This month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">94.2%</div>
            <p className="text-xs text-muted-foreground">AI task success rate</p>
          </CardContent>
        </Card>
      </div>

      {/* Ferrari AI Services Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {ferrariServices.map((service, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{service.name}</CardTitle>
                <Badge variant="outline" className="text-green-500">
                  <CheckCircle className="mr-1 h-3 w-3" />
                  {service.status}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">AI Confidence</span>
                  <span className="text-sm text-muted-foreground">{service.confidence}%</span>
                </div>
                <Progress value={service.confidence} className="h-2" />
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Tasks Completed</span>
                <span className="text-sm text-muted-foreground">{service.tasks}</span>
              </div>

              <div className="flex gap-2 pt-2">
                <Button size="sm" className="flex-1">Execute</Button>
                <Button size="sm" variant="outline">Configure</Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Real-time Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Real-time AI Activity</CardTitle>
          <CardDescription>Live Ferrari AI processing status</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-4 p-3 rounded-lg border">
              <div className="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center">
                <Brain className="h-4 w-4 text-blue-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium">Creative Exploit Generation</p>
                <p className="text-xs text-muted-foreground">Generating novel payloads for web-app.example.com</p>
              </div>
              <Badge variant="secondary">Running</Badge>
            </div>

            <div className="flex items-center gap-4 p-3 rounded-lg border">
              <div className="h-8 w-8 rounded-full bg-green-100 dark:bg-green-900/20 flex items-center justify-center">
                <Network className="h-4 w-4 text-green-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium">Multi-Stage Attack Analysis</p>
                <p className="text-xs text-muted-foreground">Completed attack chain for 192.168.1.0/24</p>
              </div>
              <Badge variant="secondary">Completed</Badge>
            </div>

            <div className="flex items-center gap-4 p-3 rounded-lg border">
              <div className="h-8 w-8 rounded-full bg-purple-100 dark:bg-purple-900/20 flex items-center justify-center">
                <Activity className="h-4 w-4 text-purple-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium">Behavioral Pattern Detection</p>
                <p className="text-xs text-muted-foreground">Analyzing API traffic patterns</p>
              </div>
              <Badge variant="secondary">Running</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}