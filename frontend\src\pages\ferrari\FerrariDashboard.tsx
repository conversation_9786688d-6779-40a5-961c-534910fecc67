/**
 * Ferrari Dashboard - Main Ferrari AI platform overview
 */
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Brain,
  Zap,
  Target,
  Network,
  Shield,
  TrendingUp,
  Activity,
  CheckCircle,
  AlertTriangle,
  RefreshCw,
  Eye,
  Workflow,
  Sparkles
} from 'lucide-react';

// Import stores
import { useBackendStore } from '@/stores/backend-store';

interface FerrariService {
  id: string;
  name: string;
  status: 'online' | 'offline' | 'degraded';
  confidence: number;
  description: string;
  icon: React.ComponentType;
  route: string;
  available: boolean;
}

export function FerrariDashboard() {
  const navigate = useNavigate();
  const { status, aiStatus, refreshAIStatus } = useBackendStore();
  const [loading, setLoading] = useState(false);
  const [ferrariServices, setFerrariServices] = useState<FerrariService[]>([]);

  useEffect(() => {
    if (status.connected) {
      refreshAIStatus();
    }
  }, [status.connected, refreshAIStatus]);

  useEffect(() => {
    // Convert backend AI status to Ferrari services
    if (aiStatus?.data) {
      const services: FerrariService[] = [
        {
          id: 'creative-exploits',
          name: 'Creative Exploits',
          status: aiStatus.data.advanced_capabilities?.creative_exploit_engine?.available ? 'online' : 'offline',
          confidence: aiStatus.data.advanced_capabilities?.creative_exploit_engine?.available ? 92 : 0,
          description: 'AI-generated novel attack vectors and polyglot payload construction',
          icon: Sparkles,
          route: '/ferrari/creative-exploits',
          available: aiStatus.data.advanced_capabilities?.creative_exploit_engine?.available || false
        },
        {
          id: 'orchestrator',
          name: 'Multi-Stage Orchestrator',
          status: aiStatus.data.advanced_capabilities?.multi_stage_orchestrator?.available ? 'online' : 'offline',
          confidence: aiStatus.data.advanced_capabilities?.multi_stage_orchestrator?.available ? 88 : 0,
          description: 'AI-powered attack chain orchestration with MITRE ATT&CK integration',
          icon: Workflow,
          route: '/ferrari/orchestrator',
          available: aiStatus.data.advanced_capabilities?.multi_stage_orchestrator?.available || false
        },
        {
          id: 'behavioral-analysis',
          name: 'Behavioral Analysis',
          status: aiStatus.data.advanced_capabilities?.behavioral_analysis_engine?.available ? 'online' : 'offline',
          confidence: aiStatus.data.advanced_capabilities?.behavioral_analysis_engine?.available ? 85 : 0,
          description: 'Zero-day style behavioral pattern recognition and anomaly detection',
          icon: Eye,
          route: '/ferrari/behavioral-analysis',
          available: aiStatus.data.advanced_capabilities?.behavioral_analysis_engine?.available || false
        },
        {
          id: 'ai-proxy',
          name: 'AI Proxy',
          status: 'offline', // Not implemented yet
          confidence: 0,
          description: 'Intelligent proxy rotation and traffic analysis for detection avoidance',
          icon: Network,
          route: '/ferrari/ai-proxy',
          available: false
        },
        {
          id: 'evasion-generator',
          name: 'Evasion Generator',
          status: aiStatus.data.advanced_capabilities?.evasion_technique_generator?.available ? 'online' : 'offline',
          confidence: aiStatus.data.advanced_capabilities?.evasion_technique_generator?.available ? 87 : 0,
          description: 'Advanced WAF, IPS, and security control bypass generation',
          icon: Shield,
          route: '/ferrari/evasion-techniques',
          available: aiStatus.data.advanced_capabilities?.evasion_technique_generator?.available || false
        },
        {
          id: 'adaptive-modifier',
          name: 'Adaptive Modifier',
          status: aiStatus.data.advanced_capabilities?.adaptive_exploit_modifier?.available ? 'online' : 'offline',
          confidence: aiStatus.data.advanced_capabilities?.adaptive_exploit_modifier?.available ? 89 : 0,
          description: 'Real-time exploit adaptation based on target environment analysis',
          icon: Target,
          route: '/ferrari/adaptive-modifier',
          available: aiStatus.data.advanced_capabilities?.adaptive_exploit_modifier?.available || false
        }
      ];
      setFerrariServices(services);
    }
  }, [aiStatus]);

  const handleRefresh = async () => {
    setLoading(true);
    try {
      await refreshAIStatus();
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'text-green-500';
      case 'degraded': return 'text-yellow-500';
      case 'offline': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'online': return 'default';
      case 'degraded': return 'secondary';
      case 'offline': return 'destructive';
      default: return 'outline';
    }
  };

  const onlineServices = ferrariServices.filter(s => s.status === 'online').length;
  const totalServices = ferrariServices.length;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Ferrari AI Platform</h1>
          <p className="text-muted-foreground">Advanced AI-powered security testing capabilities</p>
        </div>
        <div className="flex items-center gap-3">
          <Badge variant={status.connected ? 'default' : 'destructive'}>
            <Activity className="mr-1 h-3 w-3" />
            {status.connected ? 'Backend Connected' : 'Backend Offline'}
          </Badge>
          <Badge variant="outline" className={onlineServices === totalServices ? 'text-green-500' : 'text-yellow-500'}>
            <Zap className="mr-1 h-3 w-3" />
            {onlineServices}/{totalServices} Systems Online
          </Badge>
          <Button onClick={handleRefresh} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh Status
          </Button>
        </div>
      </div>

      {/* Connection Warning */}
      {!status.connected && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-3">
              <AlertTriangle className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="font-medium text-yellow-800">Backend Connection Required</p>
                <p className="text-sm text-yellow-600 mt-1">
                  Connect to the backend server (161.97.99.62:8090) to access Ferrari AI capabilities.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* AI Performance Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">AI Services</CardTitle>
            <Brain className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{onlineServices}/{totalServices}</div>
            <p className="text-xs text-muted-foreground">
              {onlineServices === totalServices ? 'All services online' : `${totalServices - onlineServices} offline`}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">AI Confidence</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {ferrariServices.length > 0
                ? Math.round(ferrariServices.reduce((acc, s) => acc + s.confidence, 0) / ferrariServices.length)
                : 0}%
            </div>
            <p className="text-xs text-muted-foreground">Average across all services</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">AI Providers</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {aiStatus?.data?.core_ai_services?.active_providers?.length || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {aiStatus?.data?.core_ai_services?.active_providers?.join(', ') || 'No providers'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Status</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {aiStatus?.data?.overall_status === 'online' ? 'Online' : 'Offline'}
            </div>
            <p className="text-xs text-muted-foreground">
              {aiStatus?.data?.last_updated
                ? `Updated ${new Date(aiStatus.data.last_updated).toLocaleTimeString()}`
                : 'Never updated'
              }
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Ferrari AI Services Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {ferrariServices.map((service) => {
          const IconComponent = service.icon;
          return (
            <Card
              key={service.id}
              className="hover:shadow-lg transition-shadow cursor-pointer"
              onClick={() => navigate(service.route)}
            >
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <IconComponent className="h-5 w-5 text-purple-600" />
                    </div>
                    <CardTitle className="text-lg">{service.name}</CardTitle>
                  </div>
                  <Badge variant={getStatusBadgeVariant(service.status)} className={getStatusColor(service.status)}>
                    <Activity className="mr-1 h-3 w-3" />
                    {service.status}
                  </Badge>
                </div>
                <CardDescription className="mt-2">
                  {service.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">AI Confidence</span>
                    <span className="text-sm text-muted-foreground">{service.confidence}%</span>
                  </div>
                  <Progress value={service.confidence} className="h-2" />
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Status</span>
                  <span className="text-sm text-muted-foreground">
                    {service.available ? 'Ready to use' : 'Requires backend setup'}
                  </span>
                </div>

                <div className="flex gap-2 pt-2">
                  <Button
                    size="sm"
                    className="flex-1"
                    disabled={!service.available}
                    onClick={(e) => {
                      e.stopPropagation();
                      navigate(service.route);
                    }}
                  >
                    {service.available ? 'Launch' : 'Setup Required'}
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={(e) => {
                      e.stopPropagation();
                      navigate(service.route);
                    }}
                  >
                    View Details
                  </Button>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Real-time Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Real-time AI Activity</CardTitle>
          <CardDescription>Live Ferrari AI processing status</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-4 p-3 rounded-lg border">
              <div className="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900/20 flex items-center justify-center">
                <Brain className="h-4 w-4 text-blue-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium">Creative Exploit Generation</p>
                <p className="text-xs text-muted-foreground">Generating novel payloads for web-app.example.com</p>
              </div>
              <Badge variant="secondary">Running</Badge>
            </div>

            <div className="flex items-center gap-4 p-3 rounded-lg border">
              <div className="h-8 w-8 rounded-full bg-green-100 dark:bg-green-900/20 flex items-center justify-center">
                <Network className="h-4 w-4 text-green-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium">Multi-Stage Attack Analysis</p>
                <p className="text-xs text-muted-foreground">Completed attack chain for 192.168.1.0/24</p>
              </div>
              <Badge variant="secondary">Completed</Badge>
            </div>

            <div className="flex items-center gap-4 p-3 rounded-lg border">
              <div className="h-8 w-8 rounded-full bg-purple-100 dark:bg-purple-900/20 flex items-center justify-center">
                <Activity className="h-4 w-4 text-purple-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium">Behavioral Pattern Detection</p>
                <p className="text-xs text-muted-foreground">Analyzing API traffic patterns</p>
              </div>
              <Badge variant="secondary">Running</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}